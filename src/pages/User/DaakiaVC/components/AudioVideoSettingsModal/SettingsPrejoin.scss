@import "../../styles/variables";

.settings-prejoin-modal {
  // Ensure perfect centering
  .ant-modal-content {
    border-radius: 12px;
    overflow: hidden;
    font-family: 'Inter', sans-serif;
    height: 70vh;


    .ant-modal-header {
        padding: 15px 30px 15px 30px;
      border-bottom: 1px solid #D7D7D7;
      margin-bottom: 0;
      flex-shrink: 0;

      .ant-modal-title {
        margin: 0;
        padding: 0;
        line-height: 1;
        width: 100%;
        height: 100%;
      }

      .custom-modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        height: 100%;

        .custom-modal-title {
          font-size: 18px;
          font-weight: 500;
          color: #636363;
          flex: 1;
        }

        .custom-close-button {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 36px;
          height: 36px;
          border-radius: 50%;
          cursor: pointer;
          transition: all 0.3s ease;
          color: #666;
          font-size: 16px;

          &:hover {
            background-color: #f5f5f5;
            color: #333;
          }
        }
      }
    }

    // Hide default close button
    .ant-modal-close {
      display: none;
    }

    .ant-modal-body {
      padding: 0;
      height: calc(100% - 60px);
      overflow: hidden;

      .settings-tabs {
        height: 100%;
        display: flex;
        flex-direction: row;

        .ant-tabs-nav {
          width: 240px;
          margin: 0;
          flex-shrink: 0;
          background-color: #f7f7f7;
          padding: 15px;


          .ant-tabs-nav-list {
            width: 100%;


            .ant-tabs-tab {
              padding: 10px;
              margin: 0;
              border-radius: 0;
              width: 100%;
              border-radius: 5px;

              &.ant-tabs-tab-active {
                background-color: #e4e9f5;
                border-color: transparent;
              }

              .tab-label {
                font-family: $font;
                font-weight: 500;
                font-size: 16px;
                display: flex;
                align-items: center;
                gap: 0.75rem;
                width: 100%;

                svg {
                  width: 22px;
                  height: 22px;
                  flex-shrink: 0;
                }

                span {
                  white-space: nowrap;
                }
              }
            }
          }
        }

        .ant-tabs-content-holder {
          border-left: 2px solid #eff1f4;
          height: 100%;
          overflow: hidden;

          .ant-tabs-content {
            height: 100%;

            .ant-tabs-tabpane {
              height: 100%;
              padding: 0;
              overflow-y: auto;
              scrollbar-width: thin;
              scrollbar-color: transparent transparent;
              transition: scrollbar-color 0.3s ease;

              // Webkit browsers (Chrome, Safari, Edge)
              &::-webkit-scrollbar {
                width: 6px;
              }

              &::-webkit-scrollbar-track {
                background: transparent;
                border-radius: 3px;
              }

              &::-webkit-scrollbar-thumb {
                background: transparent;
                border-radius: 3px;
                transition: background 0.3s ease;
              }

              // Show scrollbar on hover
              &:hover {
                scrollbar-color: #888 #f1f1f1;

                &::-webkit-scrollbar-track {
                  background: #f1f1f1;
                }

                &::-webkit-scrollbar-thumb {
                  background: #888;
                }
              }

              // Show scrollbar during scroll
              &.scrolling {
                scrollbar-color: #888 #f1f1f1;

                &::-webkit-scrollbar-track {
                  background: #f1f1f1;
                }

                &::-webkit-scrollbar-thumb {
                  background: #888;
                }
              }
            }
          }
        }

        .ant-tabs-ink-bar {
          display: none;
        }
      }
    }
  }
}

.settings-content {
  padding: 0 1rem 0 1rem;
  height: auto;
  max-height: 100%;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
  transition: scrollbar-color 0.3s ease;

  // Webkit browsers (Chrome, Safari, Edge)
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: transparent;
    border-radius: 3px;
    transition: background 0.3s ease;
  }

  // Show scrollbar on hover
  &:hover {
    scrollbar-color: #888 #f1f1f1;

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
    }

    &::-webkit-scrollbar-thumb {
      background: #888;
    }
  }

  // Show scrollbar during scroll
  &.scrolling {
    scrollbar-color: #888 #f1f1f1;

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
    }

    &::-webkit-scrollbar-thumb {
      background: #888;
    }
  }
}

.settings-section {
  margin-bottom: 2rem;

  // Add bottom border for microphone, speaker, and camera sections
  &.microphone-settings-section,
  &.speaker-settings-section,
  &.camera-settings-section {
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .settings-header {
    display: flex;
    align-items: flex-end;
    gap: 1rem;
    margin-bottom: 1.5rem;
    border-bottom: 1px solid #f0f0f0;
    padding: 15px 0px 15px 0px;

    h3 {
      font-size: 20px;
      font-family: Inter;
      font-weight: 600;
      color: #3B60E4;
      margin: 0;
      line-height: 1;
    }

    .settings-description {
      color: #666;
      font-size: 12px;
      margin: 0;
      line-height: 1;
      padding-bottom: 2.3px;
    }
  }

  h3 {
    font-size: 22px;
    font-family: $font;
    font-weight: 600;
    color: #000;
    margin-bottom: 0.5rem;
  }

  .settings-description {
    color: #666;
    font-size: 14px;
    margin-bottom: 1.5rem;
  }
}

// Grid-based layout system
.grid-container {
  display: grid;
  gap: 1rem;
  width: 100%;
  margin-bottom: 1rem;

  &.microphone-grid {
    grid-template-columns: 150px 200px 2fr;
    grid-template-rows: repeat(3, auto);
  }

  &.speaker-grid {
    grid-template-columns: 150px 200px 2fr;
    grid-template-rows: repeat(4, auto);
  }

  &.camera-grid {
    grid-template-columns: 150px 200px 2fr;
    grid-template-rows: auto;
  }

  .grid-cell {
    display: flex;
    align-items: center;
    min-height: 50px;
    padding: 4px 8px;
    // Remove all visual container styling
    background-color: transparent;
    border: none;
    border-radius: 0;

    &.center {
      justify-content: center;
      text-align: center;
    }

    &.left {
      justify-content: flex-start;
      text-align: left;
    }

    // Special styling for cells with controls - retain original element styles
    .settings-test-button {
      width: auto; // Don't take full width
      max-width: none;
    }

    .audio-level-blocks,
    .mic-level-indicator,
    .volume-control {
      width: 100%;
      max-width: 100%;
    }

    .audio-level-blocks {
      min-width: auto; // Override the 250px min-width for grid layout
    }

    .mic-level-indicator {
      min-width: auto; // Override the 250px min-width for grid layout
    }

    .volume-control {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
    }
  }
}

.setting-row {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  width: 100%;

  &.three-column {
    display: grid;
    grid-template-columns: 200px 1fr 1fr;
    gap: 1rem;
    align-items: start;
  }

  .setting-label-col {
    display: flex;
    align-items: flex-start;
    height: 100%;
    min-height: 40px;
    padding-top: 0;
  }

  .setting-description-col {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
    min-height: 160px; // Adjust based on content
  }

  .setting-control-col {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
    min-height: 160px; // Match description column
  }

  .setting-item {
    display: flex;
    align-items: center;
    min-height: 40px;
    margin-bottom: 0.5rem;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .setting-label {
    font-weight: 500;
    font-size: 16px;
    color: #333;
    margin: 0;
  }

  .setting-sublabel {
    font-size: 14px;
    color: #666;
    margin: 0;
  }

  .settings-test-button {
    height: 40px;
    padding: 0 16px;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    background: #fff;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 120px;
    font-size: 14px;
    font-weight: 500;
    color: #333;

    &:hover:not(:disabled) {
      border-color: #40a9ff;
      color: #40a9ff;
      background: #f8fbff;
    }

    &.testing {
      background: #f5f5f5;
      border-color: #d9d9d9;
      color: #999;
    }

    &:disabled {
      background: #f5f5f5;
      border-color: #d9d9d9;
      color: #999;
      cursor: not-allowed;
      opacity: 0.6;
    }
  }

  .audio-level-blocks {
    display: flex;
    align-items: center;
    gap: 4px;
    height: 40px;
    width: 100%;
    min-width: 250px;

    .audio-block {
      flex: 1;
      background-color: #e8e8e8;
      border-radius: 2px;
      transition: background-color 0.1s ease-in-out;

      &.active {
        background-color: #40a9ff;
      }
    }
  }
}

// New Microphone Level Indicator Styles
.mic-level-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  height: 40px;
  width: 100%;
  min-width: 250px;
  padding: 4px 0; // Only top and bottom padding, no left/right

  .mic-bar {
    flex: 1; // Each bar takes equal share of available width
    height: 24px;
    border-radius: 2px;
    transition: background-color 0.1s ease-in-out;
    background-color: #e8e8e8;

    &.active {
      background-color: #40a9ff;
    }
  }
}

// Device dropdown styles (from MicDeviceDropdown.scss)
.device-settings-dropdown {
  .ant-dropdown-menu {
    background: #FFFFFF;
    border-radius: 8px !important;
    min-width: 250px;
    max-width: 90vw;
    padding: 4px;
    border: 1px solid #E3E3E3;
    margin-top: 8px;

    .ant-dropdown-menu-item {
      padding: 0;
      margin: 2px 0;
      width: 100%;

      &:hover {
        background: #F5F5F5;
      }
    }
  }
}

.device-dropdown-item {
  display: flex;
  align-items: center;
  padding: 6px;
  cursor: pointer;
  border-radius: 4px;
  width: 100%;
  height: 100%;

  &.selected {
    background-color: #E3E3E3;
    padding: 6px;
  }

  &.disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }

  .device-name {
    font-size: 13px;
    color: #555454;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
  }
}

.custom-device-select {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: 1px solid #a8a8a8;
  border-radius: 3px;
  padding: 0.4rem 0.8rem;
  font-size: 13px;
  color: #555454;
  font-weight: 500;
  background-color: #fff;
  cursor: pointer;
  width: 100%;
  min-height: 40px;

  .device-select-text {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-right: 8px;
  }

  .dropdown-arrow {
    color: #555454;
    font-size: 12px;
  }

  &:hover {
    border-color: #999;
  }

  &:disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }
}

.setting-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;

  .setting-icon {
    width: 24px;
    height: 24px;
    background: linear-gradient(90deg, rgba(59, 96, 228, 1) 0%, rgba(86, 123, 255, 1) 100%);
    border-radius: 50%;
    padding: 4px;
    flex-shrink: 0;
  }

  .setting-details {
    display: flex;
    flex-direction: column;

    .setting-label {
      font-size: 16px;
      font-weight: 500;
      color: #000;
      font-family: $font;
    }

    .setting-sublabel {
      font-size: 12px;
      color: #666;
      margin-top: 2px;
    }
  }

  .setting-label {
    font-size: 16px;
    font-weight: 500;
    color: #000;
    font-family: $font;
  }
}

.setting-control {
  display: flex;
  align-items: center;
  gap: 0.5rem;

  .ant-select {
    .ant-select-selector {
      border: 1px solid #d9d9d9;
      border-radius: 6px;

      &:hover {
        border-color: #3B60E4;
      }
    }

    &.ant-select-focused .ant-select-selector {
      border-color: #3B60E4;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
  }



  .ant-switch-checked {
    background-color: #277bf7 !important;
  }

  .switch-status {
    font-size: 12px;
    color: #666;
    margin-left: 0.5rem;
  }

  .test-button {
    background-color: #f0f0f0;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    padding: 6px 12px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s;

    &:hover:not(:disabled) {
      background-color: #e6f7ff;
      border-color: #40a9ff;
      color: #1890ff;
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }

  .level-indicator {
    width: 200px;

    .ant-progress {
      .ant-progress-bg {
        transition: width 0.1s ease;
      }
    }
  }

  .brightness-value {
    font-family: $font;
    font-weight: 500;
    color: #666;
    min-width: 40px;
    text-align: right;
    margin-left: 0.5rem;
  }

  // Ensure brightness slider gets blue styling
  .ant-slider {
    width: 200px !important;
    min-width: 200px;

    .ant-slider-rail {
      background-color: #e8e8e8 !important;
      height: 4px !important;
      border-radius: 2px;
      width: 100% !important;
    }

    .ant-slider-track {
      background-color: #3B60E4 !important;
      height: 4px !important;
      border-radius: 2px;
    }

    .ant-slider-handle {
      border: 2px solid #3B60E4 !important;
      background-color: #ffffff !important;
      width: 16px !important;
      height: 16px !important;
      margin-top: -6px !important;

      &:hover {
        border-color: #3B60E4 !important;
        background-color: #ffffff !important;
      }

      &:focus {
        border-color: #3B60E4 !important;
        background-color: #ffffff !important;
        box-shadow: 0 0 0 2px rgba(59, 96, 228, 0.2) !important;
      }

      &:active {
        border-color: #3B60E4 !important;
        background-color: #ffffff !important;
      }
    }

    &:hover {
      .ant-slider-rail {
        background-color: #d9d9d9 !important;
      }

      .ant-slider-track {
        background-color: #3B60E4 !important;
      }

      .ant-slider-handle {
        border-color: #3B60E4 !important;
        background-color: #ffffff !important;
      }
    }
  }
}




// Slider styling - simple blue color
.ant-slider {
  width: 100%;

  // The full slider track (background)
  .ant-slider-rail {
    background-color: #e8e8e8;
    height: 4px;
    border-radius: 2px;
    width: 100%;
  }

  // The active portion of the slider (from start to handle)
  .ant-slider-track {
    background-color: #3B60E4;
    height: 4px;
    border-radius: 2px;
  }

  // The slider handle (the dot you drag)
  .ant-slider-handle {
    border: 2px solid #3B60E4;
    background-color: #ffffff;
    width: 16px;
    height: 16px;
    margin-top: -6px;

    &:hover {
      border-color: #3B60E4;
      background-color: #ffffff;
    }

    &:focus {
      border-color: #3B60E4;
      background-color: #ffffff;
      box-shadow: 0 0 0 2px rgba(59, 96, 228, 0.2);
    }

    &:active {
      border-color: #3B60E4;
      background-color: #ffffff;
    }
  }

  &:hover {
    .ant-slider-rail {
      background-color: #d9d9d9;
    }

    .ant-slider-track {
      background-color: #3B60E4;
    }

    .ant-slider-handle {
      border-color: #3B60E4;
      background-color: #ffffff;
    }
  }
}

// Responsive design
@media screen and (max-width: 1200px) {
  .settings-prejoin-modal {
    .ant-modal-content {
      width: 70vw !important;
    }
  }
}

@media screen and (max-width: 1000px) {
  .settings-prejoin-modal {
    .ant-modal-content {
      width: 80vw !important;
    }
  }
}

@media screen and (max-width: 800px) {
  .settings-prejoin-modal {
    .ant-modal-content {
      width: 90vw !important;
      height: 70vh;
    }

    .ant-modal-body {
      .settings-tabs {
        .ant-tabs-nav {
          width: 220px;
        }
      }
    }
  }

  // Grid responsive design
  .grid-container {
    &.microphone-grid,
    &.speaker-grid,
    &.camera-grid {
      grid-template-columns: 1fr;
      gap: 0.5rem;
    }

    .grid-cell {
      min-height: 40px;
      padding: 6px;
      background-color: transparent;
      border: none;

      &.center {
        justify-content: flex-start;
        text-align: left;
      }

      .settings-test-button {
        width: auto;
        max-width: none;
      }

      .audio-level-blocks,
      .mic-level-indicator,
      .volume-control {
        max-width: 100%;
      }

      .audio-level-blocks,
      .mic-level-indicator {
        min-width: auto; // Override min-width for mobile
      }
    }
  }

  .setting-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;

    &.three-column {
      display: flex;
      flex-direction: column;
      gap: 1rem;

      .setting-label-col {
        width: 100%;
        padding-top: 0;

        .setting-label {
          font-size: 18px;
          font-weight: 600;
          margin-bottom: 1rem;
        }
      }

      .setting-description-col,
      .setting-control-col {
        width: 100%;
      }

      .setting-description-col {
        .setting-item {
          margin-bottom: 0.5rem;

          .setting-sublabel {
            font-size: 16px;
            font-weight: 500;
          }
        }
      }

      .setting-control-col {
        .setting-item {
          margin-bottom: 1rem;
        }
      }
    }
  }

  .setting-control {
    width: 100%;
    justify-content: flex-end;
  }
}

@media screen and (max-width: 600px) {
  .settings-prejoin-modal {
    .ant-modal-body {
      .settings-tabs {
        .ant-tabs-nav {
          width: 200px;

          .ant-tabs-tab {
            padding: 10px;

            .tab-label {
              gap: 0.5rem;

              svg {
                width: 18px;
                height: 18px;
              }

              span {
                font-size: 14px;
              }
            }
          }
        }
      }
    }
  }
}