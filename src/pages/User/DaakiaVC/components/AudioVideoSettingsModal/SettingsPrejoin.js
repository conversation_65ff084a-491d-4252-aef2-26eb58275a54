/* eslint-disable */
import React, { useState, useEffect, useRef } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>lider, Switch, Dropdown } from "antd";
import { CloseOutlined, DownOutlined } from "@ant-design/icons";
import { PiVideoCameraFill } from "react-icons/pi";
import { ReactComponent as VoiceIcon } from "../settings/icons/VoiceIco.svg";

import { ReactComponent as MirrorSelfIcon } from "./Assets/mirrorSelf.svg";
import { ReactComponent as SpeakerIcon } from "./Assets/speaker.svg";
import { ReactComponent as SpeakerFlowIcon } from "./Assets/speakerFlow.svg";
import soundTestAudio from "./Assets/soundtest.mp3";

import "./SettingsPrejoin.scss";

export default function SettingsPrejoin({
  open,
  setOpen,
  // Audio props
  audioDeviceId,
  setAudioDeviceId,
  audioTrack,
  audioEnabled,
  setAudioEnabled,
  // Video props
  videoDeviceId,
  setVideoDeviceId,
  // Speaker props
  speakerDeviceId,
  setSpeakerDeviceId,
  // Settings props
  room,
  isSelfVideoMirrored,
  setIsSelfVideoMirrored,
  brightness = 100,
  onBrightnessChange,
  // Permission props
  permissions = { camera: false, microphone: false },
}) {
  // Device states
  const [audioDevices, setAudioDevices] = useState([]);
  const [videoDevices, setVideoDevices] = useState([]);
  const [speakerDevices, setSpeakerDevices] = useState([]);

  // Audio level states
  const [outputVolume, setOutputVolume] = useState(100);
  const [speakerAudioLevel, setSpeakerAudioLevel] = useState(0);

  // Gain node for volume control
  const gainNodeRef = useRef(null);

  // Simplified audio level state - single source of truth
  const [currentAudioLevel, setCurrentAudioLevel] = useState(0);

  // Test mic states
  const [isMicTesting, setIsMicTesting] = useState(false);
  const [micOriginalState, setMicOriginalState] = useState(null); // Store original mic state

  // Test speaker states
  const [isSpeakerTesting, setIsSpeakerTesting] = useState(false);
  const speakerTestAudioRef = useRef(null);

  // Audio settings states
  const [noiseCancellation, setNoiseCancellation] = useState(
    room?.options?.audioCaptureDefaults?.noiseSuppression || false
  );
  const [echoCancellation, setEchoCancellation] = useState(
    room?.options?.audioCaptureDefaults?.echoCancellation || false
  );
  const [autoMuteOnJoin, setAutoMuteOnJoin] = useState(true);

  // Single audio monitoring system
  const audioContextRef = useRef(null);
  const analyserRef = useRef(null);
  const animationFrameRef = useRef(null);
  const testTimeoutRef = useRef(null);

  // Speaker audio monitoring system
  const speakerAudioContextRef = useRef(null);
  const speakerAnalyserRef = useRef(null);
  const speakerAnimationFrameRef = useRef(null);

  // Scrollbar visibility refs
  const scrollTimeoutRef = useRef(null);

  // Fetch available devices
  useEffect(() => {
    const fetchDevices = async () => {
      try {
        const devices = await navigator.mediaDevices.enumerateDevices();

        const audioInputs = devices.filter(device => device.kind === 'audioinput');
        const videoInputs = devices.filter(device => device.kind === 'videoinput');
        const audioOutputs = devices.filter(device => device.kind === 'audiooutput');

        setAudioDevices(audioInputs);
        setVideoDevices(videoInputs);
        setSpeakerDevices(audioOutputs);

        // Set default speaker if none selected and devices are available
        if (audioOutputs.length > 0 && !speakerDeviceId && setSpeakerDeviceId) {
          setSpeakerDeviceId(audioOutputs[0].deviceId);
        }
      } catch (error) {
        console.error('Error fetching devices:', error);
      }
    };

    if (open && (permissions.camera || permissions.microphone)) {
      fetchDevices();
    }
  }, [open, permissions, speakerDeviceId, setSpeakerDeviceId]);



  // Unified audio level monitoring
  useEffect(() => {
    const shouldMonitor = open && audioTrack && (audioEnabled || isMicTesting);

    if (!shouldMonitor) {
      // Clean up and reset level when not monitoring
      setCurrentAudioLevel(0);
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
        animationFrameRef.current = null;
      }
      if (audioContextRef.current && audioContextRef.current.state !== 'closed') {
        try {
          audioContextRef.current.close();
        } catch (error) {
          console.warn('Error closing AudioContext:', error);
        }
        audioContextRef.current = null;
      }
      return;
    }

    const setupAudioMonitoring = async () => {
      try {
        // Clean up existing context first
        if (audioContextRef.current && audioContextRef.current.state !== 'closed') {
          try {
            audioContextRef.current.close();
          } catch (error) {
            console.warn('Error closing existing AudioContext:', error);
          }
        }
        audioContextRef.current = null;

        const stream = audioTrack.mediaStream;
        if (!stream) return;

        audioContextRef.current = new AudioContext();
        const source = audioContextRef.current.createMediaStreamSource(stream);
        analyserRef.current = audioContextRef.current.createAnalyser();

        // Optimized settings for audio monitoring
        analyserRef.current.fftSize = 2048;
        analyserRef.current.smoothingTimeConstant = isMicTesting ? 0.1 : 0.3; // More responsive during testing
        source.connect(analyserRef.current);

        const dataArray = new Uint8Array(analyserRef.current.frequencyBinCount);

        const updateLevel = () => {
          if (!analyserRef.current || !audioContextRef.current || audioContextRef.current.state === 'closed') {
            return;
          }

          analyserRef.current.getByteTimeDomainData(dataArray);

          // Calculate RMS (Root Mean Square) for audio level
          let sum = 0;
          for (let i = 0; i < dataArray.length; i += 1) {
            const sample = (dataArray[i] - 128) / 128;
            sum += sample * sample;
          }
          const rms = Math.sqrt(sum / dataArray.length);

          // Convert to percentage with scaling
          let level = rms * 100 * (isMicTesting ? 8 : 6); // Higher sensitivity during testing

          // Apply logarithmic scaling for more natural response
          if (level > 0) {
            level = Math.log10(level + 1) * (isMicTesting ? 50 : 45);
          }

          level = Math.min(100, Math.max(0, level));
          setCurrentAudioLevel(level);

          // Continue monitoring if conditions are still met
          const stillShouldMonitor = open && audioTrack && (audioEnabled || isMicTesting);
          if (stillShouldMonitor) {
            animationFrameRef.current = requestAnimationFrame(updateLevel);
          }
        };

        updateLevel();
      } catch (error) {
        console.error('Error setting up audio monitoring:', error);
        setCurrentAudioLevel(0);
      }
    };

    setupAudioMonitoring();

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
        animationFrameRef.current = null;
      }
      if (audioContextRef.current && audioContextRef.current.state !== 'closed') {
        try {
          audioContextRef.current.close();
        } catch (error) {
          console.warn('Error closing AudioContext during cleanup:', error);
        }
        audioContextRef.current = null;
      }
    };
  }, [open, audioTrack, audioEnabled, isMicTesting]);

  // Speaker audio level monitoring
  useEffect(() => {
    const shouldMonitorSpeaker = isSpeakerTesting && speakerTestAudioRef.current;

    if (!shouldMonitorSpeaker) {
      // Clean up and reset speaker level when not monitoring
      setSpeakerAudioLevel(0);
      if (speakerAnimationFrameRef.current) {
        cancelAnimationFrame(speakerAnimationFrameRef.current);
        speakerAnimationFrameRef.current = null;
      }
      if (speakerAudioContextRef.current && speakerAudioContextRef.current.state !== 'closed') {
        try {
          speakerAudioContextRef.current.close();
        } catch (error) {
          console.warn('Error closing speaker AudioContext:', error);
        }
        speakerAudioContextRef.current = null;
      }
      return;
    }

    const setupSpeakerAudioMonitoring = async () => {
      try {
        // Clean up existing context first
        if (speakerAudioContextRef.current && speakerAudioContextRef.current.state !== 'closed') {
          try {
            speakerAudioContextRef.current.close();
          } catch (error) {
            console.warn('Error closing existing speaker AudioContext:', error);
          }
        }
        speakerAudioContextRef.current = null;

        const audio = speakerTestAudioRef.current;
        if (!audio) return;

        // Try to set up audio monitoring, but don't break audio playback if it fails
        try {
          speakerAudioContextRef.current = new AudioContext();
          const source = speakerAudioContextRef.current.createMediaElementSource(audio);
          speakerAnalyserRef.current = speakerAudioContextRef.current.createAnalyser();

          // Connect source to analyser and then to destination
          source.connect(speakerAnalyserRef.current);
          speakerAnalyserRef.current.connect(speakerAudioContextRef.current.destination);

          // Optimized settings for speaker audio monitoring
          speakerAnalyserRef.current.fftSize = 2048;
          speakerAnalyserRef.current.smoothingTimeConstant = 0.2;
        } catch (audioError) {
          console.warn('Failed to set up speaker audio monitoring, continuing without level detection:', audioError);
          // Clean up failed attempt
          if (speakerAudioContextRef.current) {
            try {
              speakerAudioContextRef.current.close();
            } catch (e) {
              // Ignore cleanup errors
            }
            speakerAudioContextRef.current = null;
          }
          return; // Exit early, audio will play normally without monitoring
        }

        const dataArray = new Uint8Array(speakerAnalyserRef.current.frequencyBinCount);

        const updateSpeakerLevel = () => {
          if (!speakerAnalyserRef.current || !speakerAudioContextRef.current || speakerAudioContextRef.current.state === 'closed') {
            return;
          }

          try {
            speakerAnalyserRef.current.getByteFrequencyData(dataArray);
          } catch (error) {
            console.warn('Error getting frequency data:', error);
            return;
          }

          // Calculate average frequency data for speaker level
          let sum = 0;
          for (let i = 0; i < dataArray.length; i += 1) {
            sum += dataArray[i];
          }
          const average = sum / dataArray.length;

          // Convert to percentage with higher scaling for better sensitivity
          let level = (average / 255) * 100 * 4; // Increased from 1.5 to 4 for better visibility

          // Apply additional boost for low levels
          if (level > 0) {
            level = (level / 100) ** 0.6 * 100; // Power curve for better response
          }

          level = Math.min(100, Math.max(0, level));
          setSpeakerAudioLevel(level);

          // Continue monitoring if conditions are still met
          const stillShouldMonitor = isSpeakerTesting && speakerTestAudioRef.current;
          if (stillShouldMonitor) {
            speakerAnimationFrameRef.current = requestAnimationFrame(updateSpeakerLevel);
          }
        };

        updateSpeakerLevel();
      } catch (error) {
        console.error('Error setting up speaker audio monitoring:', error);
        setSpeakerAudioLevel(0);
      }
    };

    // Small delay to ensure audio element is ready
    setTimeout(setupSpeakerAudioMonitoring, 100);

    return () => {
      if (speakerAnimationFrameRef.current) {
        cancelAnimationFrame(speakerAnimationFrameRef.current);
        speakerAnimationFrameRef.current = null;
      }
      if (speakerAudioContextRef.current && speakerAudioContextRef.current.state !== 'closed') {
        try {
          speakerAudioContextRef.current.close();
        } catch (error) {
          console.warn('Error closing speaker AudioContext during cleanup:', error);
        }
        speakerAudioContextRef.current = null;
      }
    };
  }, [isSpeakerTesting]);

  // Handle scrollbar visibility
  useEffect(() => {
    const handleScroll = (event) => {
      const element = event.target;
      element.classList.add('scrolling');

      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }

      scrollTimeoutRef.current = setTimeout(() => {
        element.classList.remove('scrolling');
      }, 1000);
    };

    if (open) {
      const scrollableElements = document.querySelectorAll('.ant-tabs-tabpane, .settings-content');

      scrollableElements.forEach(element => {
        element.addEventListener('scroll', handleScroll, { passive: true });
      });

      return () => {
        scrollableElements.forEach(element => {
          element.removeEventListener('scroll', handleScroll);
        });

        if (scrollTimeoutRef.current) {
          clearTimeout(scrollTimeoutRef.current);
        }
      };
    }
  }, [open]);

  // Simplified test microphone function
  const testMicrophone = async () => {
    if (isMicTesting) {
      // Stop testing
      setIsMicTesting(false);

      // Clear test timeout
      if (testTimeoutRef.current) {
        clearTimeout(testTimeoutRef.current);
        testTimeoutRef.current = null;
      }

      // Restore original microphone state if we changed it
      if (micOriginalState !== null) {
        setAudioEnabled(micOriginalState);
        setMicOriginalState(null);
      }

      return;
    }

    // Start testing
    if (!audioEnabled) {
      setMicOriginalState(false); // Remember it was off
      setAudioEnabled(true);

      // Wait a bit for the audio track to be available
      setTimeout(() => {
        if (audioTrack) {
          setIsMicTesting(true);
        }
      }, 500);
    } else {
      // Mic was already on, just start testing
      setMicOriginalState(true); // Remember it was on
      setIsMicTesting(true);
    }

    // Auto-stop test after 10 seconds
    testTimeoutRef.current = setTimeout(() => {
      if (isMicTesting) {
        setIsMicTesting(false);

        // Restore original state
        if (micOriginalState !== null) {
          setAudioEnabled(micOriginalState);
          setMicOriginalState(null);
        }
      }
    }, 10000);
  };

  // Clean up when modal closes
  useEffect(() => {
    if (!open) {
      // Stop any ongoing test
      setIsMicTesting(false);

      // Clear timeouts
      if (testTimeoutRef.current) {
        clearTimeout(testTimeoutRef.current);
        testTimeoutRef.current = null;
      }

      // Restore original mic state if needed
      if (micOriginalState !== null) {
        setAudioEnabled(micOriginalState);
        setMicOriginalState(null);
      }

      // Stop speaker test and cleanup
      if (speakerTestAudioRef.current) {
        speakerTestAudioRef.current.pause();
        speakerTestAudioRef.current.currentTime = 0;
      }
      setIsSpeakerTesting(false);

      // Cleanup speaker audio monitoring
      if (speakerAnimationFrameRef.current) {
        cancelAnimationFrame(speakerAnimationFrameRef.current);
        speakerAnimationFrameRef.current = null;
      }
      if (speakerAudioContextRef.current && speakerAudioContextRef.current.state !== 'closed') {
        try {
          speakerAudioContextRef.current.close();
        } catch (error) {
          console.warn('Error closing speaker AudioContext during modal close:', error);
        }
        speakerAudioContextRef.current = null;
      }

      // Cleanup gain node
      if (gainNodeRef.current) {
        gainNodeRef.current.disconnect();
        gainNodeRef.current = null;
      }

      // Reset audio levels
      setCurrentAudioLevel(0);
      setSpeakerAudioLevel(0);
    }
  }, [open]);

  // Handle device changes
  const handleAudioDeviceChange = (deviceId) => {
    setAudioDeviceId(deviceId);
  };

  const handleVideoDeviceChange = (deviceId) => {
    setVideoDeviceId(deviceId);
  };

  const handleSpeakerDeviceChange = (deviceId) => {
    if (setSpeakerDeviceId) {
      setSpeakerDeviceId(deviceId);
    }
  };

  // Handle audio settings changes
  const handleNoiseCancellation = (checked) => {
    setNoiseCancellation(checked);
    if (room?.options?.audioCaptureDefaults) {
      room.options.audioCaptureDefaults.noiseSuppression = checked;
    }
  };

  const handleEchoCancellation = (checked) => {
    setEchoCancellation(checked);
    if (room?.options?.audioCaptureDefaults) {
      room.options.audioCaptureDefaults.echoCancellation = checked;
    }
  };

  // Handle output volume changes - control system volume and test audio
  const handleOutputVolumeChange = async (value) => {
    setOutputVolume(value);

    // Update test audio volume in real-time if it's playing
    if (isSpeakerTesting && speakerTestAudioRef.current) {
      speakerTestAudioRef.current.volume = value / 100;
    }

    try {
      // Try to control system volume using Web Audio API
      if (!audioContextRef.current) {
        if (window.AudioContext) {
          audioContextRef.current = new window.AudioContext();
        } else {
          throw new Error('Web Audio API not supported');
        }
      }

      // Create gain node if it doesn't exist
      if (!gainNodeRef.current) {
        gainNodeRef.current = audioContextRef.current.createGain();
        gainNodeRef.current.connect(audioContextRef.current.destination);
      }

      // Set the gain value (0.0 to 1.0)
      const gainValue = value / 100;
      gainNodeRef.current.gain.setValueAtTime(gainValue, audioContextRef.current.currentTime);

      console.log(`System volume set to: ${value}%`);
    } catch (error) {
      console.warn('Could not control system volume:', error);
      // Fallback: just update the state for test audio
    }
  };



  // Test functions
  const testSpeaker = async () => {
    if (isSpeakerTesting) {
      // Stop current test
      if (speakerTestAudioRef.current) {
        speakerTestAudioRef.current.pause();
        speakerTestAudioRef.current.currentTime = 0;
      }
      setIsSpeakerTesting(false);
      setSpeakerAudioLevel(0);
      return;
    }

    try {
      // Always create a fresh audio element to avoid MediaElementSource conflicts
      speakerTestAudioRef.current = new Audio(soundTestAudio);
      speakerTestAudioRef.current.preload = 'auto';

      const audio = speakerTestAudioRef.current;

      // Set volume based on output volume setting
      audio.volume = outputVolume / 100;

      // Set the audio output device if supported and speaker is selected
      if (audio.setSinkId && speakerDeviceId) {
        try {
          await audio.setSinkId(speakerDeviceId);
          console.log('Audio output set to:', speakerDeviceId);
        } catch (error) {
          console.warn('Failed to set audio output device:', error);
          // Continue with default output device
        }
      }

      // Set up event listeners
      const cleanup = () => {
        setIsSpeakerTesting(false);
        setSpeakerAudioLevel(0);
      };

      // Define event handlers without circular references
      const onEnded = () => {
        cleanup();
      };

      const onError = (error) => {
        console.error('Audio playback error:', error);
        cleanup();
      };

      audio.addEventListener('ended', onEnded, { once: true });
      audio.addEventListener('error', onError, { once: true });

      // Start playing
      setIsSpeakerTesting(true);
      await audio.play();

      console.log('Speaker test audio started playing');
    } catch (error) {
      console.error('Failed to play speaker test audio:', error);
      setIsSpeakerTesting(false);
      setSpeakerAudioLevel(0);
    }
  };

  const handleClose = () => {
    setOpen(false);
  };

  const truncateDeviceName = (name) => {
    return name; // Return full name without truncation
  };

  // Create dropdown items for microphone devices
  const createMicrophoneDropdownItems = () => {
    if (!permissions.microphone) {
      return [{
        key: 'no-permission',
        label: (
          <div className="device-dropdown-item disabled">
            <span className="device-name">Grant microphone permission to see devices</span>
          </div>
        ),
        disabled: true
      }];
    }

    return audioDevices.map((device) => ({
      key: device.deviceId,
      label: (
        <div
          className={`device-dropdown-item ${device.deviceId === audioDeviceId ? 'selected' : ''}`}
          onClick={() => handleAudioDeviceChange(device.deviceId)}
        >
          <span className="device-name">
            {truncateDeviceName(device.label || 'Default Microphone')}
          </span>
        </div>
      ),
    }));
  };

  // Create dropdown items for speaker devices
  const createSpeakerDropdownItems = () => {
    // Speaker devices are available when microphone permission is granted (needed for enumerateDevices)
    if (!permissions.microphone || speakerDevices.length === 0) {
      return [{
        key: 'no-permission',
        label: (
          <div className="device-dropdown-item disabled">
            <span className="device-name">
              {!permissions.microphone
                ? 'Grant microphone permission to see devices'
                : 'No speaker devices found'
              }
            </span>
          </div>
        ),
        disabled: true
      }];
    }

    return speakerDevices.map((device) => ({
      key: device.deviceId,
      label: (
        <div
          className={`device-dropdown-item ${device.deviceId === speakerDeviceId ? 'selected' : ''}`}
          onClick={() => handleSpeakerDeviceChange(device.deviceId)}
        >
          <span className="device-name">
            {truncateDeviceName(device.label || 'Default Speaker')}
          </span>
        </div>
      ),
    }));
  };

  // Create dropdown items for camera devices
  const createCameraDropdownItems = () => {
    if (!permissions.camera) {
      return [{
        key: 'no-permission',
        label: (
          <div className="device-dropdown-item disabled">
            <span className="device-name">Grant camera permission to see devices</span>
          </div>
        ),
        disabled: true
      }];
    }

    return videoDevices.map((device) => ({
      key: device.deviceId,
      label: (
        <div
          className={`device-dropdown-item ${device.deviceId === videoDeviceId ? 'selected' : ''}`}
          onClick={() => handleVideoDeviceChange(device.deviceId)}
        >
          <span className="device-name">
            {truncateDeviceName(device.label || 'Default Camera')}
          </span>
        </div>
      ),
    }));
  };

  // Get selected device name
  const getSelectedMicrophoneName = () => {
    const selectedDevice = audioDevices.find(device => device.deviceId === audioDeviceId);
    return truncateDeviceName(selectedDevice?.label || 'Select microphone');
  };

  const getSelectedSpeakerName = () => {
    if (!permissions.microphone) {
      return 'Grant permission to see speakers';
    }
    if (speakerDevices.length === 0) {
      return 'No speakers found';
    }
    const selectedDevice = speakerDevices.find(device => device.deviceId === speakerDeviceId);
    return truncateDeviceName(selectedDevice?.label || 'Select speaker');
  };

  const getSelectedCameraName = () => {
    if (!permissions.camera) {
      return 'Grant permission to see cameras';
    }
    if (videoDevices.length === 0) {
      return 'No cameras found';
    }
    const selectedDevice = videoDevices.find(device => device.deviceId === videoDeviceId);
    return truncateDeviceName(selectedDevice?.label || 'Select camera');
  };

  // Audio Level Blocks Component (COMMENTED OUT)
  /*
  function AudioLevelBlocks({ level, isActive = false }) {
    const numberOfBlocks = 10;
    const activeBlocks = Math.ceil((level / 100) * numberOfBlocks);

    return (
      <div className="audio-level-blocks">
        {Array.from({ length: numberOfBlocks }, (_, index) => (
          <div
            key={index}
            className={`audio-block ${
              isActive && index < activeBlocks ? 'active' : ''
            }`}
            style={{
              height: '100%', // All blocks same height
              transition: 'background-color 0.1s ease-in-out'
            }}
          />
        ))}
      </div>
    );
  }
  */

  // New Microphone Level Indicator Component
  function MicLevelIndicator({ level, isActive = false }) {
    const numberOfBars = 10;
    const activeBars = Math.ceil((level / 100) * numberOfBars);

    // Debug logging
    console.log('MicLevelIndicator Debug:', {
      level,
      isActive,
      activeBars,
      numberOfBars
    });

    return (
      <div className="mic-level-indicator">
        {Array.from({ length: numberOfBars }, (_, index) => {
          const isBarActive = isActive && index < activeBars;

          return (
            <div
              key={index}
              className={`mic-bar ${isBarActive ? 'active' : ''}`}
              style={{
                height: '24px', // Fixed height for all blocks
                flex: 1,  // Take equal share of available width (100% divided by number of blocks)
                backgroundColor: isBarActive ? '#3B60E4' : '#e8e8e8',
                borderRadius: '3px',
                transition: 'background-color 0.1s ease-in-out'
              }}
            />
          );
        })}
      </div>
    );
  }

  // Determine button text and state
  const getTestButtonText = () => {
    if (isMicTesting) {
      return 'Stop Test';
    }

    if (!audioEnabled && micOriginalState === null) {
      return 'Enable & Test Mic';
    }

    return 'Test Mic';
  };

  const getSpeakerTestButtonText = () => {
    if (isSpeakerTesting) {
      return 'Stop Test';
    }
    return 'Test Speaker';
  };

  // Tab items configuration
  const tabItems = [
    {
      key: 'audio',
      label: (
        <div className="tab-label">
          <VoiceIcon />
          <span>Audio</span>
        </div>
      ),
      children: (
        <div className="settings-content">
          <div className="settings-section">
            <div className="settings-header">
              <h3>Audio Settings</h3>
              <p className="settings-description">Change your audio settings here</p>
            </div>
          </div>

          {/* Microphone Section */}
          <div className="settings-section microphone-settings-section">
            <div className="grid-container microphone-grid">
              <div className="grid-cell left">
                <span className="setting-label">Microphone</span>
              </div>
              <div className="grid-cell left">
                <span className="setting-sublabel">Choose Microphone</span>
              </div>
              <div className="grid-cell center">
                <Dropdown
                  menu={{ items: createMicrophoneDropdownItems() }}
                  trigger={['click']}
                  placement="bottomLeft"
                  overlayClassName="device-settings-dropdown"
                  disabled={!permissions.microphone}
                >
                  <div className="custom-device-select">
                    <span className="device-select-text">
                      {getSelectedMicrophoneName()}
                    </span>
                    <DownOutlined className="dropdown-arrow" />
                  </div>
                </Dropdown>
              </div>

              <div className="grid-cell center">
                {/* Empty cell */}
              </div>
              <div className="grid-cell left">
                <span className="setting-sublabel">Test Mic</span>
              </div>
              <div className="grid-cell left">
                <div
                  onClick={!permissions.microphone ? undefined : testMicrophone}
                  style={{
                    height: '40px',
                    padding: '0 16px',
                    border: 'none',
                    borderRadius: '6px',
                    background: isMicTesting ? '#3B60E4' : '#D2D2D2',
                    color: isMicTesting ? '#fff' : '#555454',
                    fontSize: '14px',
                    fontWeight: '500',
                    minWidth: '120px',
                    cursor: !permissions.microphone ? 'not-allowed' : 'pointer',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    opacity: !permissions.microphone ? 0.5 : 1,
                    userSelect: 'none'
                  }}
                >
                  {getTestButtonText()}
                </div>
              </div>

              <div className="grid-cell center">
                {/* Empty cell */}
              </div>
              <div className="grid-cell left">
                <span className="setting-sublabel">Input Level:</span>
              </div>
              <div className="grid-cell center">
                <MicLevelIndicator
                  level={currentAudioLevel}
                  isActive={audioEnabled || isMicTesting || currentAudioLevel > 0}
                />
              </div>
            </div>
          </div>

          {/* Speaker Section */}
          <div className="settings-section speaker-settings-section">
            <div className="grid-container speaker-grid">
              <div className="grid-cell left">
                <span className="setting-label">Speaker</span>
              </div>
              <div className="grid-cell left">
                <span className="setting-sublabel">Choose Speaker</span>
              </div>
              <div className="grid-cell center">
                <Dropdown
                  menu={{ items: createSpeakerDropdownItems() }}
                  trigger={['click']}
                  placement="bottomLeft"
                  overlayClassName="device-settings-dropdown"
                  disabled={!permissions.microphone || speakerDevices.length === 0}
                >
                  <div className="custom-device-select">
                    <span className="device-select-text">
                      {getSelectedSpeakerName()}
                    </span>
                    <DownOutlined className="dropdown-arrow" />
                  </div>
                </Dropdown>
              </div>

              <div className="grid-cell center">
                {/* Empty cell */}
              </div>
              <div className="grid-cell left">
                <span className="setting-sublabel">Test Speaker</span>
              </div>
              <div className="grid-cell left">
                <div
                  onClick={(!permissions.microphone || speakerDevices.length === 0) ? undefined : testSpeaker}
                  style={{
                    height: '40px',
                    padding: '0 16px',
                    border: 'none',
                    borderRadius: '6px',
                    background: isSpeakerTesting ? '#3B60E4' : '#D2D2D2',
                    color: isSpeakerTesting ? '#fff' : '#555454',
                    fontSize: '14px',
                    fontWeight: '500',
                    minWidth: '120px',
                    cursor: (!permissions.microphone || speakerDevices.length === 0) ? 'not-allowed' : 'pointer',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    opacity: (!permissions.microphone || speakerDevices.length === 0) ? 0.5 : 1,
                    userSelect: 'none'
                  }}
                >
                  {getSpeakerTestButtonText()}
                </div>
              </div>

              <div className="grid-cell center">
                {/* Empty cell */}
              </div>
              <div className="grid-cell left">
                <span className="setting-sublabel">Output Level:</span>
              </div>
              <div className="grid-cell center">
                <MicLevelIndicator
                  level={speakerAudioLevel}
                  isActive={isSpeakerTesting || speakerAudioLevel > 0}
                />
              </div>

              <div className="grid-cell center">
                {/* Empty cell */}
              </div>
              <div className="grid-cell left">
                <span className="setting-sublabel">Output Volume:</span>
              </div>
              <div className="grid-cell center">
                <div className="volume-control">
                  <SpeakerIcon className="slider-icon" style={{ marginRight: '12px' }} />
                  <Slider
                    value={outputVolume}
                    onChange={handleOutputVolumeChange}
                    min={0}
                    max={100}
                    tooltip={{ formatter: (value) => `${value}%` }}
                  />
                  <SpeakerFlowIcon className="slider-icon" style={{ marginLeft: '12px' }} />
                </div>
              </div>
            </div>
          </div>

          {/* Audio Enhancement Section */}
          <div className="settings-section">
            <div className="setting-row">
              <div className="setting-info">
                <div className="setting-details">
                  <span className="setting-label">Noise cancellation</span>
                  <span className="setting-sublabel">Remove background noise to improve call quality</span>
                </div>
              </div>
              <div className="setting-control">
                <Switch
                  checked={noiseCancellation}
                  onChange={handleNoiseCancellation}
                />
                <span className="switch-status">
                  {noiseCancellation ? 'Enabled' : 'Disabled'}
                </span>
              </div>
            </div>
            <div className="setting-border"></div>

            <div className="setting-row">
              <div className="setting-info">
                <div className="setting-details">
                  <span className="setting-label">Echo cancellation</span>
                  <span className="setting-sublabel">Remove background noise to improve call quality</span>
                </div>
              </div>
              <div className="setting-control">
                <Switch
                  checked={echoCancellation}
                  onChange={handleEchoCancellation}
                />
                <span className="switch-status">
                  {echoCancellation ? 'Enabled' : 'Disabled'}
                </span>
              </div>
            </div>
            <div className="setting-border"></div>

            <div className="setting-row">
              <div className="setting-info">
                <div className="setting-details">
                  <span className="setting-label">Auto-mute on joining meeting</span>
                  <span className="setting-sublabel">Remove background noise to improve call quality</span>
                </div>
              </div>
              <div className="setting-control">
                <Switch
                  checked={autoMuteOnJoin}
                  onChange={setAutoMuteOnJoin}
                />
                <span className="switch-status">
                  {autoMuteOnJoin ? 'Enabled' : 'Disabled'}
                </span>
              </div>
            </div>
          </div>
        </div>
      ),
    },
    {
      key: 'video',
      label: (
        <div className="tab-label">
          <PiVideoCameraFill />
          <span>Video</span>
        </div>
      ),
      children: (
        <div className="settings-content">
          <div className="settings-section">
            <div className="settings-header">
              <h3>Video Settings</h3>
              <p className="settings-description">Change your video settings here</p>
            </div>
          </div>

          {/* Camera Section */}
          <div className="settings-section camera-settings-section">
            <div className="grid-container camera-grid">
              <div className="grid-cell left">
                <span className="setting-label">Camera</span>
              </div>
              <div className="grid-cell left">
                <span className="setting-sublabel">Choose Camera</span>
              </div>
              <div className="grid-cell center">
                <Dropdown
                  menu={{ items: createCameraDropdownItems() }}
                  trigger={['click']}
                  placement="bottomLeft"
                  overlayClassName="device-settings-dropdown"
                  disabled={!permissions.camera}
                >
                  <div className="custom-device-select">
                    <span className="device-select-text">
                      {getSelectedCameraName()}
                    </span>
                    <DownOutlined className="dropdown-arrow" />
                  </div>
                </Dropdown>
              </div>
            </div>
          </div>

          {/* Video Enhancement Section */}
          <div className="settings-section">
            <div className="setting-row">
              <div className="setting-info">
                <MirrorSelfIcon className="setting-icon" />
                <div className="setting-details">
                  <span className="setting-label">Mirror video</span>
                  <span className="setting-sublabel">Flip your video horizontally to correct the orientation</span>
                </div>
              </div>
              <div className="setting-control">
                <Switch
                  checked={isSelfVideoMirrored}
                  onChange={setIsSelfVideoMirrored}
                />
                <span className="switch-status">
                  {isSelfVideoMirrored ? 'Enabled' : 'Disabled'}
                </span>
              </div>
            </div>

            <div className="setting-row">
              <div className="setting-info">
                <div className="setting-details">
                  <span className="setting-label">Video brightness</span>
                  <span className="setting-sublabel">Adjust the brightness of your video to improve visibility</span>
                </div>
              </div>
              <div className="setting-control">
                <Slider
                  value={brightness}
                  onChange={onBrightnessChange}
                  min={50}
                  max={150}
                  tooltip={{ formatter: (value) => `${value}%` }}
                />
                <span className="brightness-value">{brightness}%</span>
              </div>
            </div>
          </div>
        </div>
      ),
    },
  ];

  return (
    <Modal
      open={open}
      onOk={handleClose}
      onCancel={handleClose}
      className="settings-prejoin-modal"
      footer={null}
      width="90%"
      style={{ maxWidth: '1000px' }}
      closeIcon={null}
      centered
      title={
        <div className="custom-modal-header">
          <span className="custom-modal-title">Settings</span>
          <div className="custom-close-button" onClick={handleClose}>
            <CloseOutlined />
          </div>
        </div>
      }
    >
      <Tabs
        defaultActiveKey="audio"
        items={tabItems}
        tabPosition="left"
        className="settings-tabs"
      />
    </Modal>
  );
}